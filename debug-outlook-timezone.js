// Debug the timezone conversion issue for Outlook
function formatDateTimeForICal(date, time = '00:00', timezone = 'Europe/London') {
  // Validate inputs
  if (!date || typeof date !== 'string') {
    throw new Error('Date is required and must be a string')
  }
  
  const timeString = time || '00:00'
  
  // Validate date format
  if (!date.match(/^\d{4}-\d{2}-\d{2}$/)) {
    throw new Error(`Invalid date format: ${date}. Expected YYYY-MM-DD format.`)
  }
  
  // Validate time format (accept both HH:MM and HH:MM:SS)
  if (!timeString.match(/^\d{2}:\d{2}(:\d{2})?$/)) {
    throw new Error(`Invalid time format: ${timeString}. Expected HH:MM or HH:MM:SS format.`)
  }
  
  // Normalize to HH:MM format (remove seconds if present)
  const normalizedTime = timeString.substring(0, 5)
  
  try {
    // MOST RELIABLE APPROACH: Iterative timezone conversion
    // Create a UTC timestamp that represents the moment when it's the specified time in the target timezone
    
    // Parse the components
    const [year, month, day] = date.split('-').map(Number)
    const [hours, minutes] = normalizedTime.split(':').map(Number)
    
    // Create a date object representing our target time (initially in local browser timezone)
    const tentativeDate = new Date(year, month - 1, day, hours, minutes, 0)
    
    // Function to get timezone offset for any UTC timestamp in the target timezone
    function getTimezoneOffset(utcTimestamp, tz) {
      const utcDate = new Date(utcTimestamp)
      const tzString = utcDate.toLocaleString('sv-SE', { timeZone: tz })
      const tzDate = new Date(tzString)
      return utcDate.getTime() - tzDate.getTime()
    }
    
    // Use iterative approach to find the correct UTC time
    let utcGuess = tentativeDate.getTime()
    
    // Iteratively refine our guess (usually converges in 1-2 iterations)
    for (let i = 0; i < 5; i++) {
      const offset = getTimezoneOffset(utcGuess, timezone)
      const adjustedUtc = utcGuess - offset
      
      // Verify this gives us the right local time
      const testDate = new Date(adjustedUtc)
      const testLocal = testDate.toLocaleString('sv-SE', { timeZone: timezone })
      const [testDatePart, testTimePart] = testLocal.split(' ')
      
      if (testDatePart === date && testTimePart.startsWith(normalizedTime)) {
        // Found the correct UTC time!
        const utcDateTime = new Date(adjustedUtc)
        
        if (isNaN(utcDateTime.getTime())) {
          throw new Error('Failed to convert to UTC')
        }
        
        // Format as iCal requires (YYYYMMDDTHHMMSSZ)
        const utcYear = utcDateTime.getUTCFullYear()
        const utcMonth = String(utcDateTime.getUTCMonth() + 1).padStart(2, '0')
        const utcDay = String(utcDateTime.getUTCDate()).padStart(2, '0')
        const utcHours = String(utcDateTime.getUTCHours()).padStart(2, '0')
        const utcMinutes = String(utcDateTime.getUTCMinutes()).padStart(2, '0')
        const utcSeconds = String(utcDateTime.getUTCSeconds()).padStart(2, '0')
        
        return `${utcYear}${utcMonth}${utcDay}T${utcHours}${utcMinutes}${utcSeconds}Z`
      }
      
      utcGuess = adjustedUtc
    }
    
    throw new Error('Could not converge on correct UTC time')
    
  } catch (error) {
    throw new Error(`Timezone conversion failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

console.log('=== Debugging Outlook Timezone Issue ===')

// Test the problematic case: 11:00-12:00 BST
const today = new Date().toISOString().split('T')[0] // Use today's date
console.log(`Testing with today's date: ${today}`)

console.log('\n--- Testing 11:00 AM BST ---')
const start11am = formatDateTimeForICal(today, '11:00', 'Europe/London')
console.log(`Input: ${today} 11:00 Europe/London`)
console.log(`iCal Output: ${start11am}`)

// Parse the result to verify
const startParsed = new Date(start11am.replace(/(\d{4})(\d{2})(\d{2})T(\d{2})(\d{2})(\d{2})Z/, '$1-$2-$3T$4:$5:$6Z'))
console.log(`Parsed UTC: ${startParsed.toISOString()}`)
console.log(`Shows as London time: ${startParsed.toLocaleString('en-GB', { timeZone: 'Europe/London' })}`)

console.log('\n--- Testing 12:00 PM BST ---')
const end12pm = formatDateTimeForICal(today, '12:00', 'Europe/London')
console.log(`Input: ${today} 12:00 Europe/London`)
console.log(`iCal Output: ${end12pm}`)

const endParsed = new Date(end12pm.replace(/(\d{4})(\d{2})(\d{2})T(\d{2})(\d{2})(\d{2})Z/, '$1-$2-$3T$4:$5:$6Z'))
console.log(`Parsed UTC: ${endParsed.toISOString()}`)
console.log(`Shows as London time: ${endParsed.toLocaleString('en-GB', { timeZone: 'Europe/London' })}`)

console.log('\n--- Duration Check ---')
const durationMs = endParsed.getTime() - startParsed.getTime()
const durationHours = durationMs / (1000 * 60 * 60)
console.log(`Duration: ${durationHours} hours`)

console.log('\n--- Current Timezone Info ---')
console.log(`Local timezone offset: ${new Date().getTimezoneOffset()} minutes`)
console.log(`Current time: ${new Date().toISOString()}`)
console.log(`Current London time: ${new Date().toLocaleString('en-GB', { timeZone: 'Europe/London' })}`)

// Test what happens if we create the date differently
console.log('\n--- Alternative Approach Test ---')
function alternativeConversion(date, time, timezone = 'Europe/London') {
  const dateTimeString = `${date}T${time}:00`
  
  // Create a date assuming it's in the specified timezone
  // Method 1: Use a fixed approach for London specifically
  if (timezone === 'Europe/London') {
    const localDate = new Date(dateTimeString)
    
    // Get current date to check if we're in BST or GMT
    const testDate = new Date()
    const utcTime = testDate.getTime()
    const londonTime = new Date(testDate.toLocaleString('en-US', { timeZone: 'Europe/London' })).getTime()
    const offset = (utcTime - londonTime) / (1000 * 60 * 60) // hours
    
    console.log(`Current London offset from UTC: ${offset} hours`)
    
    // Apply the correct offset
    const utcDate = new Date(localDate.getTime() + (offset * 60 * 60 * 1000))
    
    const year = utcDate.getUTCFullYear()
    const month = String(utcDate.getUTCMonth() + 1).padStart(2, '0')
    const day = String(utcDate.getUTCDate()).padStart(2, '0')
    const hours = String(utcDate.getUTCHours()).padStart(2, '0')
    const minutes = String(utcDate.getUTCMinutes()).padStart(2, '0')
    const seconds = String(utcDate.getUTCSeconds()).padStart(2, '0')
    
    return `${year}${month}${day}T${hours}${minutes}${seconds}Z`
  }
  
  return formatDateTimeForICal(date, time, timezone)
}

const altStart = alternativeConversion(today, '11:00', 'Europe/London')
const altEnd = alternativeConversion(today, '12:00', 'Europe/London')
console.log(`Alternative start: ${altStart}`)
console.log(`Alternative end: ${altEnd}`)

// Verify these times
const altStartParsed = new Date(altStart.replace(/(\d{4})(\d{2})(\d{2})T(\d{2})(\d{2})(\d{2})Z/, '$1-$2-$3T$4:$5:$6Z'))
const altEndParsed = new Date(altEnd.replace(/(\d{4})(\d{2})(\d{2})T(\d{2})(\d{2})(\d{2})Z/, '$1-$2-$3T$4:$5:$6Z'))
console.log(`Alt start shows as London: ${altStartParsed.toLocaleString('en-GB', { timeZone: 'Europe/London' })}`)
console.log(`Alt end shows as London: ${altEndParsed.toLocaleString('en-GB', { timeZone: 'Europe/London' })}`)
