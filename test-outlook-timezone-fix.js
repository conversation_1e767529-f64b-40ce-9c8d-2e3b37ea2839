// Test the new timezone-aware iCal generation for Outlook
function escapeICalText(text) {
  if (!text) return ''
  
  return text
    .replace(/\\/g, '\\\\')    // Escape backslashes first
    .replace(/;/g, '\\;')      // Escape semicolons
    .replace(/,/g, '\\,')      // Escape commas
    .replace(/\n/g, '\\n')     // Escape newlines
    .replace(/\r/g, '')        // Remove carriage returns
    .trim()
}

function generateUID() {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 15)
  const domain = 'nexzero.app'
  return `${timestamp}-${random}@${domain}`
}

function formatDateTimeForOutlook(date, time = '00:00', timezone = 'Europe/London') {
  const timeString = time || '00:00'
  const normalizedTime = timeString.substring(0, 5) // Remove seconds if present
  
  // Format as YYYYMMDDTHHMMSS (no Z suffix - local time)
  const [year, month, day] = date.split('-')
  const [hours, minutes] = normalizedTime.split(':')
  
  return `${year}${month}${day}T${hours}${minutes}00`
}

function addHour(timeString) {
  const [hours, minutes] = timeString.split(':').map(Number)
  let newHours = hours + 1
  
  if (newHours >= 24) {
    newHours = 0 // Wrap to next day (caller should handle date change)
  }
  
  return `${String(newHours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`
}

function generateICalContent(calendarEvent) {
  // Generate proper UID and timestamp for Outlook compatibility
  const uid = generateUID()
  const now = new Date()
  
  // Create proper DTSTAMP in UTC format
  const year = now.getUTCFullYear()
  const month = String(now.getUTCMonth() + 1).padStart(2, '0')
  const day = String(now.getUTCDate()).padStart(2, '0')
  const hours = String(now.getUTCHours()).padStart(2, '0')
  const minutes = String(now.getUTCMinutes()).padStart(2, '0')
  const seconds = String(now.getUTCSeconds()).padStart(2, '0')
  const timestamp = `${year}${month}${day}T${hours}${minutes}${seconds}Z`

  // Created timestamp (same as DTSTAMP for new events)
  const created = timestamp

  let icalContent = [
    'BEGIN:VCALENDAR',
    'VERSION:2.0',
    'PRODID:-//Nexzero//Event Calendar//EN',
    'CALSCALE:GREGORIAN',
    'METHOD:PUBLISH'
  ]

  // Add timezone definition for better Outlook compatibility
  if (calendarEvent.timezone === 'Europe/London') {
    icalContent.push(
      'BEGIN:VTIMEZONE',
      'TZID:Europe/London',
      'BEGIN:DAYLIGHT',
      'TZOFFSETFROM:+0000',
      'TZOFFSETTO:+0100',
      'TZNAME:BST',
      'DTSTART:19700329T010000',
      'RRULE:FREQ=YEARLY;BYMONTH=3;BYDAY=-1SU',
      'END:DAYLIGHT',
      'BEGIN:STANDARD',
      'TZOFFSETFROM:+0100',
      'TZOFFSETTO:+0000',
      'TZNAME:GMT',
      'DTSTART:19701025T020000',
      'RRULE:FREQ=YEARLY;BYMONTH=10;BYDAY=-1SU',
      'END:STANDARD',
      'END:VTIMEZONE'
    )
  }

  // Convert to timezone-specific format for Outlook
  const startLocal = formatDateTimeForOutlook(calendarEvent.startDate, calendarEvent.startTime, calendarEvent.timezone)
  const endLocal = formatDateTimeForOutlook(
    calendarEvent.endDate || calendarEvent.startDate, 
    calendarEvent.endTime || (calendarEvent.startTime ? addHour(calendarEvent.startTime) : '01:00'),
    calendarEvent.timezone
  )

  icalContent.push(
    'BEGIN:VEVENT',
    `UID:${uid}`,
    `DTSTAMP:${timestamp}`,
    `CREATED:${created}`,
    `DTSTART;TZID=${calendarEvent.timezone}:${startLocal}`,
    `DTEND;TZID=${calendarEvent.timezone}:${endLocal}`,
    `SUMMARY:${escapeICalText(calendarEvent.title)}`,
    'STATUS:CONFIRMED',          // Outlook prefers explicit status
    'TRANSP:OPAQUE',            // Show as busy
    'SEQUENCE:0',               // Version of this event
    'CLASS:PUBLIC'              // Public event
  )

  if (calendarEvent.description) {
    icalContent.push(`DESCRIPTION:${escapeICalText(calendarEvent.description)}`)
  }

  if (calendarEvent.location) {
    icalContent.push(`LOCATION:${escapeICalText(calendarEvent.location)}`)
  }

  if (calendarEvent.url) {
    icalContent.push(`URL:${calendarEvent.url}`)
  }

  // Add last modified timestamp (same as created for new events)
  icalContent.push(`LAST-MODIFIED:${timestamp}`)

  icalContent.push('END:VEVENT', 'END:VCALENDAR')

  // Use \r\n line endings for maximum compatibility
  return icalContent.join('\r\n')
}

console.log('=== Testing New Timezone-Aware iCal for Outlook ===')

const testEvent = {
  title: 'Test Meeting 11:00-12:00 BST',
  description: 'This should show as 11:00-12:00 in UK time',
  startDate: '2025-07-06',
  startTime: '11:00',
  endDate: '2025-07-06',
  endTime: '12:00',
  timezone: 'Europe/London',
  location: 'London Office'
}

const icsContent = generateICalContent(testEvent)

console.log('\n=== Generated ICS Content ===')
console.log(icsContent)

console.log('\n=== Key Changes for Outlook ===')
console.log('✓ Added VTIMEZONE definition for Europe/London')
console.log('✓ Using DTSTART;TZID=Europe/London format')
console.log('✓ Local time format (no Z suffix)')
console.log('✓ Explicit timezone information')

// Extract the times to verify
const lines = icsContent.split('\n')
const dtstart = lines.find(line => line.startsWith('DTSTART'))
const dtend = lines.find(line => line.startsWith('DTEND'))

console.log(`\nDTSTART: ${dtstart}`)
console.log(`DTEND: ${dtend}`)
console.log('\nThis should now display correctly in Outlook as 11:00-12:00 UK time')
