import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@14.21.0'

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
})

const supabaseClient = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

const webhookSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET')!

serve(async (req) => {
  const signature = req.headers.get('stripe-signature')
  
  if (!signature) {
    return new Response('No signature', { status: 400 })
  }

  try {
    const body = await req.text()
    const event = stripe.webhooks.constructEvent(body, signature, webhookSecret)

    console.log(`Processing webhook event: ${event.type}`)

    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session)
        break
      
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription)
        break
      
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription)
        break
      
      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.Invoice)
        break
      
      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.Invoice)
        break
      
      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return new Response(JSON.stringify({ received: true }), {
      headers: { 'Content-Type': 'application/json' },
      status: 200,
    })
  } catch (error) {
    console.error('Webhook error:', error)
    return new Response(`Webhook error: ${error.message}`, { status: 400 })
  }
})

async function handleCheckoutSessionCompleted(session: Stripe.Checkout.Session) {
  const userId = session.metadata?.user_id
  const planId = session.metadata?.plan_id

  if (!userId || !planId) {
    console.error('Missing metadata in checkout session')
    return
  }

  // Get the subscription from Stripe
  if (session.subscription) {
    const subscription = await stripe.subscriptions.retrieve(session.subscription as string)
    await createOrUpdateSubscription(subscription, userId, planId)
  }
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  const userId = subscription.metadata?.user_id
  const planId = subscription.metadata?.plan_id

  if (!userId || !planId) {
    console.error('Missing metadata in subscription')
    return
  }

  await createOrUpdateSubscription(subscription, userId, planId)
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  const userId = subscription.metadata?.user_id

  if (!userId) {
    console.error('Missing user_id in subscription metadata')
    return
  }

  // Update subscription status to canceled
  await supabaseClient
    .from('user_subscriptions')
    .update({
      status: 'canceled',
      canceled_at: new Date().toISOString(),
    })
    .eq('stripe_subscription_id', subscription.id)

  // Update profile subscription plan to free
  await supabaseClient
    .from('profiles')
    .update({
      current_subscription_plan: 'free',
      subscription_expires_at: null,
    })
    .eq('id', userId)
}

async function createOrUpdateSubscription(subscription: Stripe.Subscription, userId: string, planId: string) {
  const subscriptionData = {
    user_id: userId,
    plan_id: planId,
    stripe_customer_id: subscription.customer as string,
    stripe_subscription_id: subscription.id,
    status: subscription.status as any,
    current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
    current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
    cancel_at_period_end: subscription.cancel_at_period_end,
    canceled_at: subscription.canceled_at ? new Date(subscription.canceled_at * 1000).toISOString() : null,
  }

  // Upsert subscription
  const { error: subscriptionError } = await supabaseClient
    .from('user_subscriptions')
    .upsert(subscriptionData, {
      onConflict: 'stripe_subscription_id'
    })

  if (subscriptionError) {
    console.error('Error upserting subscription:', subscriptionError)
    return
  }

  // Get plan type for profile update
  const { data: plan } = await supabaseClient
    .from('subscription_plans')
    .select('plan_type')
    .eq('id', planId)
    .single()

  if (plan) {
    // Update profile with current subscription plan
    await supabaseClient
      .from('profiles')
      .update({
        current_subscription_plan: plan.plan_type,
        subscription_expires_at: subscription.status === 'active' 
          ? new Date(subscription.current_period_end * 1000).toISOString()
          : null,
      })
      .eq('id', userId)
  }
}

async function handlePaymentSucceeded(invoice: Stripe.Invoice) {
  const subscription = await stripe.subscriptions.retrieve(invoice.subscription as string)
  const userId = subscription.metadata?.user_id

  if (!userId) {
    console.error('Missing user_id in subscription metadata')
    return
  }

  // Record payment in history
  const paymentData = {
    user_id: userId,
    stripe_payment_intent_id: invoice.payment_intent as string,
    stripe_invoice_id: invoice.id,
    amount: invoice.amount_paid,
    currency: invoice.currency,
    status: 'succeeded' as const,
    description: invoice.description || 'Subscription payment',
    payment_date: new Date(invoice.status_transitions.paid_at! * 1000).toISOString(),
  }

  // Get subscription record
  const { data: userSubscription } = await supabaseClient
    .from('user_subscriptions')
    .select('id')
    .eq('stripe_subscription_id', subscription.id)
    .single()

  if (userSubscription) {
    paymentData.subscription_id = userSubscription.id
  }

  await supabaseClient
    .from('payment_history')
    .insert(paymentData)
}

async function handlePaymentFailed(invoice: Stripe.Invoice) {
  const subscription = await stripe.subscriptions.retrieve(invoice.subscription as string)
  const userId = subscription.metadata?.user_id

  if (!userId) {
    console.error('Missing user_id in subscription metadata')
    return
  }

  // Record failed payment in history
  const paymentData = {
    user_id: userId,
    stripe_payment_intent_id: invoice.payment_intent as string,
    stripe_invoice_id: invoice.id,
    amount: invoice.amount_due,
    currency: invoice.currency,
    status: 'failed' as const,
    description: invoice.description || 'Subscription payment (failed)',
    payment_date: new Date().toISOString(),
  }

  // Get subscription record
  const { data: userSubscription } = await supabaseClient
    .from('user_subscriptions')
    .select('id')
    .eq('stripe_subscription_id', subscription.id)
    .single()

  if (userSubscription) {
    paymentData.subscription_id = userSubscription.id
  }

  await supabaseClient
    .from('payment_history')
    .insert(paymentData)
}
