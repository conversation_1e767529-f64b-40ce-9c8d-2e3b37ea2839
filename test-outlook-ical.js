// Test the enhanced Outlook-compatible iCal generation
function formatDateTimeForICal(date, time = '00:00', timezone = 'Europe/London') {
  // Validate inputs
  if (!date || typeof date !== 'string') {
    throw new Error('Date is required and must be a string')
  }
  
  const timeString = time || '00:00'
  
  // Validate date format
  if (!date.match(/^\d{4}-\d{2}-\d{2}$/)) {
    throw new Error(`Invalid date format: ${date}. Expected YYYY-MM-DD format.`)
  }
  
  // Validate time format (accept both HH:MM and HH:MM:SS)
  if (!timeString.match(/^\d{2}:\d{2}(:\d{2})?$/)) {
    throw new Error(`Invalid time format: ${timeString}. Expected HH:MM or HH:MM:SS format.`)
  }
  
  // Normalize to HH:MM format (remove seconds if present)
  const normalizedTime = timeString.substring(0, 5)
  
  try {
    // MOST RELIABLE APPROACH: Iterative timezone conversion
    // Create a UTC timestamp that represents the moment when it's the specified time in the target timezone
    
    // Parse the components
    const [year, month, day] = date.split('-').map(Number)
    const [hours, minutes] = normalizedTime.split(':').map(Number)
    
    // Create a date object representing our target time (initially in local browser timezone)
    const tentativeDate = new Date(year, month - 1, day, hours, minutes, 0)
    
    // Function to get timezone offset for any UTC timestamp in the target timezone
    function getTimezoneOffset(utcTimestamp, tz) {
      const utcDate = new Date(utcTimestamp)
      const tzString = utcDate.toLocaleString('sv-SE', { timeZone: tz })
      const tzDate = new Date(tzString)
      return utcDate.getTime() - tzDate.getTime()
    }
    
    // Use iterative approach to find the correct UTC time
    let utcGuess = tentativeDate.getTime()
    
    // Iteratively refine our guess (usually converges in 1-2 iterations)
    for (let i = 0; i < 5; i++) {
      const offset = getTimezoneOffset(utcGuess, timezone)
      const adjustedUtc = utcGuess - offset
      
      // Verify this gives us the right local time
      const testDate = new Date(adjustedUtc)
      const testLocal = testDate.toLocaleString('sv-SE', { timeZone: timezone })
      const [testDatePart, testTimePart] = testLocal.split(' ')
      
      if (testDatePart === date && testTimePart.startsWith(normalizedTime)) {
        // Found the correct UTC time!
        const utcDateTime = new Date(adjustedUtc)
        
        if (isNaN(utcDateTime.getTime())) {
          throw new Error('Failed to convert to UTC')
        }
        
        // Format as iCal requires (YYYYMMDDTHHMMSSZ)
        const utcYear = utcDateTime.getUTCFullYear()
        const utcMonth = String(utcDateTime.getUTCMonth() + 1).padStart(2, '0')
        const utcDay = String(utcDateTime.getUTCDate()).padStart(2, '0')
        const utcHours = String(utcDateTime.getUTCHours()).padStart(2, '0')
        const utcMinutes = String(utcDateTime.getUTCMinutes()).padStart(2, '0')
        const utcSeconds = String(utcDateTime.getUTCSeconds()).padStart(2, '0')
        
        return `${utcYear}${utcMonth}${utcDay}T${utcHours}${utcMinutes}${utcSeconds}Z`
      }
      
      utcGuess = adjustedUtc
    }
    
    throw new Error('Could not converge on correct UTC time')
    
  } catch (error) {
    throw new Error(`Timezone conversion failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

function escapeICalText(text) {
  if (!text) return ''
  
  return text
    .replace(/\\/g, '\\\\')    // Escape backslashes first
    .replace(/;/g, '\\;')      // Escape semicolons
    .replace(/,/g, '\\,')      // Escape commas
    .replace(/\n/g, '\\n')     // Escape newlines
    .replace(/\r/g, '')        // Remove carriage returns
    .trim()
}

function generateUID() {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 15)
  const domain = 'nexzero.app'
  return `${timestamp}-${random}@${domain}`
}

function generateICalContent(calendarEvent) {
  const startDateTime = formatDateTimeForICal(
    calendarEvent.startDate, 
    calendarEvent.startTime, 
    calendarEvent.timezone
  )
  
  let endDateTime
  if (calendarEvent.endDate && calendarEvent.endTime) {
    endDateTime = formatDateTimeForICal(
      calendarEvent.endDate, 
      calendarEvent.endTime, 
      calendarEvent.timezone
    )
  } else {
    // Default to 1 hour duration if no end time specified
    const startDate = new Date(`${calendarEvent.startDate}T${calendarEvent.startTime || '00:00'}`)
    startDate.setHours(startDate.getHours() + 1)
    endDateTime = formatDateTimeForICal(
      startDate.toISOString().split('T')[0],
      startDate.toTimeString().slice(0, 5),
      calendarEvent.timezone
    )
  }

  // Generate proper UID and timestamp for Outlook compatibility
  const uid = generateUID()
  const now = new Date()
  
  // Create proper DTSTAMP in UTC format
  const year = now.getUTCFullYear()
  const month = String(now.getUTCMonth() + 1).padStart(2, '0')
  const day = String(now.getUTCDate()).padStart(2, '0')
  const hours = String(now.getUTCHours()).padStart(2, '0')
  const minutes = String(now.getUTCMinutes()).padStart(2, '0')
  const seconds = String(now.getUTCSeconds()).padStart(2, '0')
  const timestamp = `${year}${month}${day}T${hours}${minutes}${seconds}Z`

  // Created timestamp (same as DTSTAMP for new events)
  const created = timestamp

  let icalContent = [
    'BEGIN:VCALENDAR',
    'VERSION:2.0',
    'PRODID:-//Nexzero//Event Calendar//EN',
    'CALSCALE:GREGORIAN',
    'METHOD:PUBLISH',
    'BEGIN:VEVENT',
    `UID:${uid}`,
    `DTSTAMP:${timestamp}`,
    `CREATED:${created}`,
    `DTSTART:${startDateTime}`,
    `DTEND:${endDateTime}`,
    `SUMMARY:${escapeICalText(calendarEvent.title)}`,
    'STATUS:CONFIRMED',          // Outlook prefers explicit status
    'TRANSP:OPAQUE',            // Show as busy
    'SEQUENCE:0',               // Version of this event
    'CLASS:PUBLIC'              // Public event
  ]

  if (calendarEvent.description) {
    icalContent.push(`DESCRIPTION:${escapeICalText(calendarEvent.description)}`)
  }

  if (calendarEvent.location) {
    icalContent.push(`LOCATION:${escapeICalText(calendarEvent.location)}`)
  }

  if (calendarEvent.url) {
    icalContent.push(`URL:${calendarEvent.url}`)
  }

  // Add last modified timestamp (same as created for new events)
  icalContent.push(`LAST-MODIFIED:${timestamp}`)

  icalContent.push('END:VEVENT', 'END:VCALENDAR')

  // Use \r\n line endings for maximum compatibility
  return icalContent.join('\r\n')
}

// Test the enhanced iCal generation
console.log('=== Testing Enhanced Outlook-Compatible iCal Generation ===')

const testEvent = {
  title: 'Test Meeting with Special Characters; Comma, Newline\nHere',
  description: 'This is a test meeting with:\n- Special characters: ; , \\\n- Multiple lines\n- Unicode: £€§',
  startDate: '2025-07-06',
  startTime: '11:00',
  endDate: '2025-07-06', 
  endTime: '12:00',
  timezone: 'Europe/London',
  location: 'Conference Room A, Building B; Floor 3',
  url: 'https://nexzero.app/events/test'
}

const icsContent = generateICalContent(testEvent)

console.log('\n=== Generated ICS Content ===')
console.log(icsContent)

console.log('\n=== Key Features for Outlook Compatibility ===')
console.log('✓ Proper UID format with domain')
console.log('✓ DTSTAMP in correct UTC format')
console.log('✓ CREATED timestamp included')
console.log('✓ STATUS, TRANSP, SEQUENCE, CLASS fields')
console.log('✓ LAST-MODIFIED timestamp')
console.log('✓ Special characters properly escaped')
console.log('✓ \\r\\n line endings')
console.log('✓ METHOD:PUBLISH specified')

// Test timezone conversion
console.log('\n=== Timezone Conversion Test ===')
console.log('Input: 2025-07-06 11:00 Europe/London (BST)')
console.log('Expected UTC: 10:00 (BST is UTC+1)')
const testStart = formatDateTimeForICal('2025-07-06', '11:00', 'Europe/London')
console.log(`Actual UTC: ${testStart}`)
console.log(`Correct: ${testStart === '20250706T100000Z' ? '✓' : '✗'}`)
